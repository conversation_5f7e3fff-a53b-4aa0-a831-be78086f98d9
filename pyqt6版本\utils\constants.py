"""
常量定义模块
包含应用程序使用的各种常量
"""

# Emoji 常量
EMOJI = {
    "INFO": "ℹ️",
    "ERROR": "❌", 
    "SUCCESS": "✅",
    "WARNING": "⚠️",
    "LOGIN": "🔑",
    "EMAIL": "📧",
    "USER": "👤",
    "COPY": "📋",
    "RESET": "🔄",
    "START": "▶️",
    "STOP": "⏹️",
    "CONFIG": "⚙️",
    "APP": "🔑"
}

# 颜色常量
COLORS = {
    # 主色调
    'PRIMARY': '#2196F3',
    'PRIMARY_DARK': '#1976D2',
    'PRIMARY_LIGHT': '#BBDEFB',
    
    # 次要色调
    'SECONDARY': '#FF9800',
    'SECONDARY_DARK': '#F57C00',
    'SECONDARY_LIGHT': '#FFE0B2',
    
    # 状态颜色
    'SUCCESS': '#4CAF50',
    'WARNING': '#FF9800',
    'ERROR': '#F44336',
    'INFO': '#2196F3',
    
    # 背景色
    'BACKGROUND': '#121212',
    'SURFACE': '#1E1E1E',
    'CARD': '#2D2D2D',
    
    # 文本颜色
    'TEXT_PRIMARY': '#FFFFFF',
    'TEXT_SECONDARY': '#B0B0B0',
    'TEXT_DISABLED': '#666666',
    
    # 边框颜色
    'BORDER': '#404040',
    'BORDER_LIGHT': '#606060',
    
    # 按钮颜色
    'BUTTON_NORMAL': '#2196F3',
    'BUTTON_HOVER': '#1976D2',
    'BUTTON_PRESSED': '#0D47A1',
    'BUTTON_DISABLED': '#424242'
}

# 尺寸常量
SIZES = {
    # 窗口尺寸
    'WINDOW_MIN_WIDTH': 800,
    'WINDOW_MIN_HEIGHT': 600,
    'WINDOW_DEFAULT_WIDTH': 1000,
    'WINDOW_DEFAULT_HEIGHT': 700,
    
    # 组件尺寸
    'BUTTON_HEIGHT': 36,
    'INPUT_HEIGHT': 32,
    'TOOLBAR_HEIGHT': 48,
    'SIDEBAR_WIDTH': 200,
    
    # 间距
    'MARGIN_SMALL': 8,
    'MARGIN_MEDIUM': 16,
    'MARGIN_LARGE': 24,
    'MARGIN_XLARGE': 32,
    
    # 圆角
    'BORDER_RADIUS': 6,
    'BORDER_RADIUS_LARGE': 12,
    
    # 字体大小
    'FONT_SIZE_SMALL': 10,
    'FONT_SIZE_NORMAL': 12,
    'FONT_SIZE_LARGE': 14,
    'FONT_SIZE_XLARGE': 16,
    'FONT_SIZE_TITLE': 18
}

# 邮箱服务器配置
EMAIL_CONFIG = {
    'POP3_SERVER': 'pop3.2925.com',
    'POP3_PORT': 995,
    'SMTP_SERVER': 'smtp.2925.com',
    'SMTP_PORT': 587,
    'EMAIL_DOMAIN': '2925.com'
}

# 应用程序配置
APP_CONFIG = {
    'APP_NAME': 'Cursor管理工具',
    'APP_VERSION': '2.0.0',
    'CONFIG_DIR': '.cursor-free-vip',
    'CONFIG_FILE': 'config.ini',
    'REGISTRY_KEY': r'SOFTWARE\CursorVipTool'
}
