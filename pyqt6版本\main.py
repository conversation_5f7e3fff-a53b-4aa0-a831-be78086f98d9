#!/usr/bin/env python3
"""
Cursor管理工具 - PyQt6版本
主程序入口文件

作者: Cursor管理工具团队
版本: 2.0.0
"""

import sys
import os
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt6.QtWidgets import QApplication, QMessageBox
    from PyQt6.QtCore import Qt, QTranslator, QLocale
    from PyQt6.QtGui import QIcon, QFont
except ImportError as e:
    print("❌ 导入PyQt6失败，请确保已安装PyQt6:")
    print("   pip install PyQt6")
    print(f"   错误详情: {e}")
    sys.exit(1)

from ui.main_window import MainWindow
from resources.styles.dark_theme import DarkTheme
from utils.constants import APP_CONFIG
from utils.helpers import safe_log, get_system_info


class CursorManagerApp(QApplication):
    """Cursor管理工具应用程序类"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # 设置应用程序基本信息
        self.setApplicationName(APP_CONFIG['APP_NAME'])
        self.setApplicationVersion(APP_CONFIG['APP_VERSION'])
        self.setOrganizationName("Cursor管理工具团队")
        
        # 设置应用程序属性
        self.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        self.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        
        # 初始化应用程序
        self.init_app()
    
    def init_app(self):
        """初始化应用程序"""
        try:
            # 设置字体
            self.setup_font()
            
            # 应用主题
            self.apply_theme()
            
            # 创建主窗口
            self.main_window = MainWindow()
            
            # 显示主窗口
            self.main_window.show()
            
            safe_log("应用程序初始化完成")
            
        except Exception as e:
            self.show_error("应用程序初始化失败", str(e))
            sys.exit(1)
    
    def setup_font(self):
        """设置应用程序字体"""
        try:
            # 设置默认字体
            font = QFont()
            font.setFamily("Microsoft YaHei UI, Segoe UI, Arial")
            font.setPointSize(9)
            self.setFont(font)
            
        except Exception as e:
            safe_log(f"设置字体失败: {e}", 'warning')
    
    def apply_theme(self):
        """应用主题样式"""
        try:
            # 应用深色主题
            dark_theme = DarkTheme()
            self.setStyleSheet(dark_theme.get_stylesheet())
            
            safe_log("深色主题应用成功")
            
        except Exception as e:
            safe_log(f"应用主题失败: {e}", 'warning')
    
    def show_error(self, title: str, message: str):
        """显示错误对话框"""
        try:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setWindowTitle(f"❌ {title}")
            msg_box.setText(message)
            msg_box.setDetailedText(traceback.format_exc())
            msg_box.exec()
        except Exception:
            # 如果连对话框都无法显示，则输出到控制台
            print(f"❌ {title}: {message}")
            print(traceback.format_exc())


def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")
    
    try:
        import pywinauto
    except ImportError:
        missing_deps.append("pywinauto")
    
    if missing_deps:
        print("❌ 缺少以下依赖项:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n请运行以下命令安装依赖:")
        print("   pip install -r requirements.txt")
        return False
    
    return True


def main():
    """主函数"""
    try:
        # 检查依赖项
        if not check_dependencies():
            sys.exit(1)
        
        # 输出系统信息
        system_info = get_system_info()
        safe_log(f"系统信息: {system_info['system']} {system_info['version']}")
        safe_log(f"Python版本: {system_info['python_version'].split()[0]}")
        safe_log(f"管理员权限: {'是' if system_info['is_admin'] else '否'}")
        
        # 创建应用程序实例
        app = CursorManagerApp(sys.argv)
        
        # 运行应用程序
        safe_log("启动Cursor管理工具...")
        exit_code = app.exec()
        
        safe_log("应用程序退出")
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        safe_log("用户中断程序运行")
        sys.exit(0)
    except Exception as e:
        safe_log(f"程序运行出错: {e}", 'error')
        print(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
