# 🔑 Cursor管理工具 - PyQt6版本

## 📋 项目简介

这是Cursor管理工具的PyQt6重写版本，提供更加现代化和美观的用户界面。

## ✨ 主要特性

- 🎨 **现代化UI设计** - 基于PyQt6的美观界面
- 🌙 **深色主题支持** - 护眼的深色主题
- 📱 **响应式布局** - 适配不同屏幕尺寸
- ⚡ **高性能** - 优化的代码结构和性能
- 🔒 **安全配置** - 支持注册表和文件配置
- 📧 **邮箱管理** - 智能邮箱监控和管理
- 🔄 **应用管理** - Cursor进程和ID管理

## 🚀 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行程序

```bash
python main.py
```

## 📁 项目结构

```
pyqt6版本/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖列表
├── core/                   # 核心模块
│   ├── __init__.py
│   ├── config_manager.py   # 配置管理
│   ├── registry_manager.py # 注册表管理
│   ├── app_manager.py      # 应用管理
│   └── email_manager.py    # 邮箱管理
├── ui/                     # 界面模块
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   ├── email_window.py     # 邮箱窗口
│   ├── config_window.py    # 配置窗口
│   └── components/         # UI组件
│       ├── __init__.py
│       ├── modern_button.py
│       ├── status_widget.py
│       └── progress_widget.py
├── resources/              # 资源文件
│   ├── __init__.py
│   ├── styles/             # 样式文件
│   │   ├── __init__.py
│   │   ├── dark_theme.py
│   │   └── modern_style.py
│   └── icons/              # 图标文件
│       └── __init__.py
└── utils/                  # 工具模块
    ├── __init__.py
    ├── constants.py        # 常量定义
    └── helpers.py          # 辅助函数
```

## 🔧 配置说明

程序支持多种配置方式：

1. **注册表配置** (Windows) - 最安全的配置方式
2. **配置文件** - 跨平台兼容的配置方式

详细配置说明请参考原版文档。

## 📝 更新日志

### v2.0.0 (PyQt6版本)
- 🎨 全新的PyQt6界面设计
- 🌙 深色主题支持
- ⚡ 性能优化和代码重构
- 📱 响应式布局设计
- 🔧 模块化架构重构
