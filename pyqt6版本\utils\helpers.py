"""
辅助函数模块
包含各种实用的辅助函数
"""

import os
import sys
import platform
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Union

from .constants import EMOJI


def safe_log(message: str, level: str = 'info') -> None:
    """
    安全的日志记录函数，根据用户偏好不生成日志文件
    
    Args:
        message: 日志消息
        level: 日志级别 ('info', 'warning', 'error', 'debug')
    """
    # 根据用户偏好，只在控制台输出，不生成日志文件
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_message = f"[{timestamp}] {level.upper()}: {message}"
    
    if level.lower() == 'error':
        print(f"❌ {formatted_message}")
    elif level.lower() == 'warning':
        print(f"⚠️ {formatted_message}")
    elif level.lower() == 'debug':
        print(f"🔍 {formatted_message}")
    else:
        print(f"ℹ️ {formatted_message}")


def get_user_documents_path() -> Optional[str]:
    """
    获取用户文档目录路径
    
    Returns:
        用户文档目录路径，如果获取失败返回None
    """
    try:
        if platform.system() == "Windows":
            import winreg
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                              r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders") as key:
                documents_path = winreg.QueryValueEx(key, "Personal")[0]
                return documents_path
        else:
            # macOS 和 Linux
            return str(Path.home() / "Documents")
    except Exception as e:
        safe_log(f"获取用户文档目录失败: {e}", 'warning')
        return None


def format_status_message(message: str, status_type: str = 'info') -> str:
    """
    格式化状态消息，添加相应的emoji图标
    
    Args:
        message: 原始消息
        status_type: 状态类型 ('info', 'success', 'warning', 'error')
    
    Returns:
        格式化后的消息
    """
    emoji_map = {
        'info': EMOJI['INFO'],
        'success': EMOJI['SUCCESS'],
        'warning': EMOJI['WARNING'],
        'error': EMOJI['ERROR']
    }
    
    emoji = emoji_map.get(status_type.lower(), EMOJI['INFO'])
    return f"{emoji} {message}"


def get_app_data_path() -> str:
    """
    获取应用程序数据目录路径
    
    Returns:
        应用程序数据目录路径
    """
    if platform.system() == "Windows":
        return os.getenv('APPDATA', os.path.expanduser('~'))
    elif platform.system() == "Darwin":  # macOS
        return os.path.expanduser('~/Library/Application Support')
    else:  # Linux
        return os.path.expanduser('~/.local/share')


def get_local_app_data_path() -> str:
    """
    获取本地应用程序数据目录路径
    
    Returns:
        本地应用程序数据目录路径
    """
    if platform.system() == "Windows":
        return os.getenv('LOCALAPPDATA', os.path.expanduser('~'))
    else:
        return get_app_data_path()


def ensure_directory_exists(path: Union[str, Path]) -> bool:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        path: 目录路径
    
    Returns:
        是否成功创建或目录已存在
    """
    try:
        Path(path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        safe_log(f"创建目录失败 {path}: {e}", 'error')
        return False


def is_admin() -> bool:
    """
    检查当前程序是否以管理员权限运行
    
    Returns:
        是否具有管理员权限
    """
    try:
        if platform.system() == "Windows":
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        else:
            return os.geteuid() == 0
    except Exception:
        return False


def get_system_info() -> dict:
    """
    获取系统信息
    
    Returns:
        包含系统信息的字典
    """
    return {
        'system': platform.system(),
        'version': platform.version(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'python_version': sys.version,
        'is_admin': is_admin()
    }
